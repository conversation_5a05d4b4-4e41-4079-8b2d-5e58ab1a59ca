import { hyperliquid<PERSON><PERSON>, arbitrum<PERSON><PERSON>, base<PERSON><PERSON>, ethereum<PERSON><PERSON> } from "@/assets/logo";

// Centralized mapping between our internal chain slug IDs and logo assets
export const NETWORK_ICONS = {
    hyperliquid: hyperliquidLogo,
    hyperevm: hyperliquidLogo,
    ethereum: ethereum<PERSON>ogo,
    base: baseLogo,
    arbitrum: arbitrumLogo,
} as const;

export type NetworkId = keyof typeof NETWORK_ICONS;

/**
 * Return logo url for provided network id. If id is unknown it returns empty string.
 */
export const getNetworkIcon = (id: string): string => {
    // Using index access so that consumers can still ask for dynamic id.
    return (NETWORK_ICONS as Record<string, string>)[id] ?? "";
}; 