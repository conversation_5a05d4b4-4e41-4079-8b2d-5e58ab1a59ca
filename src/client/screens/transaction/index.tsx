import { AccountIcon, AccountName } from "@/client/components/account";
import AccountSheet from "@/client/components/account-sheet/account-sheet";
import { LoadingDisplay, LockDisplay } from "@/client/components/display";
import { Dialog, DialogFooter } from "@/client/components/ui";
import { Button } from "@/client/components/ui/button";
import useAccountSheetStore from "@/client/hooks/use-account-sheet-store";
import useInit from "@/client/hooks/use-init";
import useWalletStore from "@/client/hooks/use-wallet-store";
import { sendMessage } from "@/client/utils/extension-message-utils";
import { formatTime, getTimeColor } from "@/client/utils/formatters";
import { Check, Clock, Globe, Shield, X, AlertTriangle } from "lucide-react";
import { useEffect, useState } from "react";
import { createRoot } from "react-dom/client";
import SignMessage from "./sign-message";
import TypedDataSign from "./typed-data";

export interface TransactionRequest {
  origin: string;
  favicon?: string;
  timestamp: number;
  type: "personal_sign" | "eth_sign" | "typed_data" | "sign"; // Added transaction signing
  data: any;
  transactionId: string;
}

const approveTransaction = (transactionId: string, result: any) =>
  sendMessage("APPROVE_TRANSACTION", { transactionId, ...result });

const rejectTransaction = (transactionId: string) =>
  sendMessage("REJECT_TRANSACTION", { transactionId });

const TransactionScreen = () => {
  const { open: openAccountSheet } = useAccountSheetStore();
  const { activeAccount } = useWalletStore();
  const [transactionRequest, setTransactionRequest] =
    useState<TransactionRequest | null>(null);
  const [loading, setLoading] = useState(false);

  useInit();

  useEffect(() => {
    const getTransactionRequest = async () => {
      const urlParams = new URLSearchParams(window.location.search);
      const origin = urlParams.get("origin") || "unknown";
      const favicon = urlParams.get("favicon") || "";
      const type =
        (urlParams.get("type") as TransactionRequest["type"]) || "send";
      const transactionId = urlParams.get("transactionId") || "";

      console.log("🔍 Loading transaction request:", {
        origin,
        type,
        transactionId,
      });

      try {
        // Try to get real pending transaction data
        const pendingTxData = await sendMessage("GET_PENDING_TRANSACTION", {
          transactionId,
        });

        if (pendingTxData) {
          console.log("✅ Found pending transaction:", pendingTxData);
          setTransactionRequest({
            origin,
            favicon,
            timestamp: pendingTxData.timestamp || Date.now(),
            type,
            data: pendingTxData.transactionData || getMockTransactionData(type),
            transactionId: transactionId,
          });
        } else {
          console.warn("⚠️ No pending transaction found, using mock data");
          // Fallback to mock data
          setTransactionRequest({
            origin,
            favicon,
            timestamp: Date.now(),
            type,
            data: getMockTransactionData(type),
            transactionId: transactionId || `${origin}_${type}_${Date.now()}`,
          });
        }
      } catch (error) {
        console.error("❌ Error loading transaction:", error);
        // Fallback to mock data
        setTransactionRequest({
          origin,
          favicon,
          timestamp: Date.now(),
          type,
          data: getMockTransactionData(type),
          transactionId: transactionId || `${origin}_${type}_${Date.now()}`,
        });
      }
    };

    getTransactionRequest();
  }, []);

  const getMockTransactionData = (type: string) => {
    switch (type) {
      case "personal_sign":
        return {
          message: "Hello, this is a test message for signing!",
          address: activeAccount?.id || "",
        };
      case "eth_sign":
        return {
          address: activeAccount?.id || "",
          message: "0x48656c6c6f20576f726c64", // "Hello World" in hex
        };
      case "typed_data":
        return {
          address: activeAccount?.id || "",
          typedData: {
            types: {
              EIP712Domain: [
                { name: "name", type: "string" },
                { name: "version", type: "string" },
              ],
              Message: [{ name: "content", type: "string" }],
            },
            domain: {
              name: "Test App",
              version: "1",
            },
            primaryType: "Message",
            message: {
              content: "Hello, World!",
            },
          },
        };
      case "sign":
        return {
          to: "******************************************",
          value: "0x0",
          gasLimit: "0x5208",
          gasPrice: "0x9184e72a000",
          nonce: "0x0",
          data: "0x",
        };
      default:
        return {};
    }
  };

  const handleApprove = async () => {
    if (!transactionRequest || !activeAccount) return;

    setLoading(true);
    try {
      console.log(
        "🔄 Approving transaction:",
        transactionRequest.type,
        "for:",
        transactionRequest.origin
      );

      // Approve transaction - real signing will be performed in background
      await approveTransaction(transactionRequest.transactionId, {});

      console.log("✅ Transaction approved successfully");

      // Small delay to ensure message is processed
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Close popup
      window.close();
    } catch (error) {
      console.error("❌ Error approving transaction:", error);
      // Don't close popup on error so user can retry
    } finally {
      setLoading(false);
    }
  };

  const handleReject = async () => {
    if (!transactionRequest) return;

    try {
      await rejectTransaction(transactionRequest.transactionId);
      window.close();
    } catch (error) {
      console.error("Error rejecting transaction:", error);
      // Still close popup even if rejection fails
      window.close();
    }
  };

  const getTransactionTitle = () => {
    switch (transactionRequest?.type) {
      case "personal_sign":
        return "Sign Message";
      case "eth_sign":
        return "Sign Message";
      case "typed_data":
        return "Sign Typed Data";
      default:
        return "Sign Request";
    }
  };

  const renderTransactionContent = () => {
    if (!transactionRequest) return null;

    switch (transactionRequest.type) {
      case "personal_sign":
      case "eth_sign":
        return (
          <SignMessage
            data={transactionRequest.data}
            type={transactionRequest.type}
          />
        );
      case "typed_data":
        return <TypedDataSign data={transactionRequest.data} />;
      case "sign":
        return (
          <div className="space-y-4">
            <div className="bg-gray-800/50 rounded-lg p-4">
              <h3 className="text-white font-medium mb-3">
                Transaction Details
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">To:</span>
                  <span className="text-white font-mono">
                    {transactionRequest.data.to}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Value:</span>
                  <span className="text-white">
                    {transactionRequest.data.value || "0 ETH"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Gas Limit:</span>
                  <span className="text-white">
                    {transactionRequest.data.gasLimit}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Gas Price:</span>
                  <span className="text-white">
                    {transactionRequest.data.gasPrice}
                  </span>
                </div>
              </div>
            </div>
          </div>
        );
      default:
        return <div>Unknown signing request</div>;
    }
  };

  if (!transactionRequest) {
    return (
      <main className="bg-[var(--background-color)] flex flex-col h-screen">
        <LoadingDisplay />
      </main>
    );
  }

  const domain = new URL(transactionRequest.origin).hostname;
  const timeAgo = formatTime(Date.now() - transactionRequest.timestamp);
  const timeColor = getTimeColor(Date.now() - transactionRequest.timestamp);

  return (
    <main className="bg-[var(--background-color)] flex flex-col h-screen">
      <LockDisplay />
      <Dialog />
      <AccountSheet />

      <div className="p-3 flex items-center justify-between border-b border-white/10">
        <div
          className="flex items-center gap-2 pr-3 hover:bg-white/10 rounded-full transition-all duration-300 cursor-pointer"
          onClick={openAccountSheet}
        >
          <div className="size-8 rounded-full bg-white/10 flex items-center justify-center cursor-pointer">
            <AccountIcon icon={activeAccount?.icon} alt="Account" />
          </div>
          <AccountName name={activeAccount?.name} />
        </div>
      </div>

      <div className="p-4 border-b border-white/10">
        <div className="flex items-center gap-3">
          {transactionRequest.favicon ? (
            <img
              src={transactionRequest.favicon}
              alt="Site favicon"
              className="size-8 rounded"
              onError={(e) => {
                (e.target as HTMLImageElement).style.display = "none";
              }}
            />
          ) : (
            <div className="size-8 rounded bg-white/10 flex items-center justify-center">
              <Globe className="size-4 text-white/60" />
            </div>
          )}
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <h2 className="text-lg font-semibold text-white">
                {getTransactionTitle()}
              </h2>
            </div>
            <div className="flex items-center gap-2 text-sm text-white/60">
              <span>{domain}</span>
              <span>•</span>
              <div className="flex items-center gap-1">
                <Clock className="size-3" />
                <span className={timeColor}>{timeAgo}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-4 p-3 rounded-lg bg-yellow-500/10 border border-yellow-500/20">
          <div className="flex items-start gap-2">
            <AlertTriangle className="size-4 text-yellow-500 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-yellow-200">
              <p className="font-medium mb-1">Review carefully</p>
              <p className="text-yellow-200/80">
                Only approve transactions you understand and trust. Malicious
                transactions can drain your wallet.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-4">
        {renderTransactionContent()}
      </div>

      <DialogFooter className="flex-col gap-2">
        <div className="flex gap-2 w-full">
          <Button
            onClick={handleReject}
            disabled={loading}
            className="bg-[var(--card-color)] text-[var(--primary-color-light)] hover:bg-[var(--card-color)]/80"
          >
            <X className="size-4" />
            Cancel
          </Button>
          <Button
            onClick={handleApprove}
            disabled={loading || !activeAccount}
            className="w-full"
          >
            {loading ? (
              <div className="flex items-center gap-2">
                <div className="size-4 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                Processing...
              </div>
            ) : (
              <>
                <Check className="size-4" />
                Confirm
              </>
            )}
          </Button>
        </div>
      </DialogFooter>
    </main>
  );
};

const container = document.getElementById("root");
if (container) {
  const root = createRoot(container);
  root.render(<TransactionScreen />);
}
