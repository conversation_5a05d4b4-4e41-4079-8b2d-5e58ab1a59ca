<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Signing Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .container {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .result {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
        }
        .error {
            background: #d32f2f;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .success {
            background: #388e3c;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            background: #333;
            color: #fff;
            border: 1px solid #555;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🔐 Direct Signing Test</h1>
    <p>This test bypasses the popup flow to test signing directly.</p>
    
    <div class="container">
        <h2>Connection Status</h2>
        <div id="connectionStatus" class="result">Not connected</div>
        <button onclick="connectWallet()">Connect Wallet</button>
        <button onclick="checkWalletState()">Check Wallet State</button>
    </div>

    <div class="container">
        <h2>Direct Personal Sign Test</h2>
        <input type="text" id="directMessage" placeholder="Message to sign" value="Hello Direct Signing!">
        <button onclick="testDirectSign()" id="directSignBtn">Test Direct Sign</button>
        <div id="directSignResult" class="result" style="display: none;"></div>
    </div>

    <script>
        let ethereum;
        let accounts = [];

        // Initialize
        window.addEventListener('load', async () => {
            if (typeof window.ethereum !== 'undefined') {
                ethereum = window.ethereum;
                console.log('Ethereum provider detected');
                checkWalletState();
            } else {
                document.getElementById('connectionStatus').innerHTML = '<span style="color: red;">No Ethereum provider detected.</span>';
            }
        });

        async function connectWallet() {
            try {
                accounts = await ethereum.request({ method: 'eth_requestAccounts' });
                document.getElementById('connectionStatus').innerHTML = `<span style="color: green;">Connected: ${accounts[0]}</span>`;
                console.log('Connected accounts:', accounts);
            } catch (error) {
                console.error('Connection error:', error);
                document.getElementById('connectionStatus').innerHTML = `<span style="color: red;">Connection failed: ${error.message}</span>`;
            }
        }

        async function checkWalletState() {
            try {
                // Check accounts
                accounts = await ethereum.request({ method: 'eth_accounts' });
                
                // Check chain
                const chainId = await ethereum.request({ method: 'eth_chainId' });
                
                // Check if wallet is unlocked by trying to get balance
                let walletState = 'Unknown';
                try {
                    if (accounts.length > 0) {
                        await ethereum.request({ 
                            method: 'eth_getBalance', 
                            params: [accounts[0], 'latest'] 
                        });
                        walletState = 'Unlocked';
                    } else {
                        walletState = 'Not connected';
                    }
                } catch (e) {
                    walletState = 'Locked or Error';
                }
                
                const status = `
                    <strong>Accounts:</strong> ${accounts.length > 0 ? accounts[0] : 'None'}<br>
                    <strong>Chain ID:</strong> ${chainId}<br>
                    <strong>Wallet State:</strong> ${walletState}<br>
                    <strong>Provider:</strong> ${ethereum.isPurro ? 'Purro' : 'Other'}
                `;
                
                document.getElementById('connectionStatus').innerHTML = status;
                console.log('Wallet state:', { accounts, chainId, walletState });
                
            } catch (error) {
                console.error('Check wallet state error:', error);
                document.getElementById('connectionStatus').innerHTML = `<span style="color: red;">Error: ${error.message}</span>`;
            }
        }

        async function testDirectSign() {
            const message = document.getElementById('directMessage').value;
            const resultDiv = document.getElementById('directSignResult');
            const btn = document.getElementById('directSignBtn');
            
            if (accounts.length === 0) {
                resultDiv.innerHTML = `<div class="error">❌ Please connect wallet first</div>`;
                resultDiv.style.display = 'block';
                return;
            }
            
            btn.disabled = true;
            btn.textContent = 'Testing...';
            
            try {
                console.log('🔄 Starting direct sign test...');
                console.log('📝 Message:', message);
                console.log('👤 Account:', accounts[0]);
                
                // Test 1: Try personal_sign
                console.log('🧪 Test 1: personal_sign');
                let signature1;
                try {
                    signature1 = await ethereum.request({
                        method: 'personal_sign',
                        params: [message, accounts[0]]
                    });
                    console.log('✅ personal_sign success:', signature1);
                } catch (e) {
                    console.error('❌ personal_sign failed:', e);
                    signature1 = `Error: ${e.message}`;
                }
                
                // Test 2: Try eth_sign
                console.log('🧪 Test 2: eth_sign');
                let signature2;
                try {
                    const hexMessage = '0x' + Buffer.from(message, 'utf8').toString('hex');
                    signature2 = await ethereum.request({
                        method: 'eth_sign',
                        params: [accounts[0], hexMessage]
                    });
                    console.log('✅ eth_sign success:', signature2);
                } catch (e) {
                    console.error('❌ eth_sign failed:', e);
                    signature2 = `Error: ${e.message}`;
                }
                
                // Test 3: Check wallet balance (to test if wallet is unlocked)
                console.log('🧪 Test 3: eth_getBalance');
                let balance;
                try {
                    balance = await ethereum.request({
                        method: 'eth_getBalance',
                        params: [accounts[0], 'latest']
                    });
                    console.log('✅ eth_getBalance success:', balance);
                } catch (e) {
                    console.error('❌ eth_getBalance failed:', e);
                    balance = `Error: ${e.message}`;
                }
                
                resultDiv.innerHTML = `
                    <div class="success">🧪 Direct Sign Test Results:</div>
                    <div>
                        <strong>Message:</strong> ${message}<br>
                        <strong>Account:</strong> ${accounts[0]}<br><br>
                        
                        <strong>personal_sign:</strong><br>
                        ${signature1}<br><br>
                        
                        <strong>eth_sign:</strong><br>
                        ${signature2}<br><br>
                        
                        <strong>Balance Check:</strong><br>
                        ${balance}
                    </div>
                `;
                resultDiv.style.display = 'block';
                
            } catch (error) {
                console.error('❌ Direct sign test error:', error);
                resultDiv.innerHTML = `<div class="error">❌ Test Error: ${error.message}</div>`;
                resultDiv.style.display = 'block';
            } finally {
                btn.disabled = false;
                btn.textContent = 'Test Direct Sign';
            }
        }
    </script>
</body>
</html>
