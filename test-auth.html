<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Purro Wallet Auth Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .container {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        .result {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
        }
        .error {
            background: #d32f2f;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .success {
            background: #388e3c;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>🔐 Purro Wallet Auth Test</h1>
    
    <div class="container">
        <h2>Authentication Test</h2>
        <button onclick="testAuth()">Test Authentication</button>
        <button onclick="testSimpleSign()">Test Simple Sign</button>
        <div id="authResult" class="result" style="display: none;"></div>
    </div>

    <script>
        async function testAuth() {
            const resultDiv = document.getElementById('authResult');
            
            try {
                console.log('🔍 Testing authentication...');
                
                // Check if wallet exists
                if (!window.ethereum) {
                    throw new Error('No Ethereum provider found');
                }
                
                console.log('✅ Ethereum provider found');
                
                // Try to get accounts
                const accounts = await window.ethereum.request({ method: 'eth_accounts' });
                console.log('📋 Accounts:', accounts);
                
                if (accounts.length === 0) {
                    // Try to connect
                    console.log('🔗 Attempting to connect...');
                    const connectedAccounts = await window.ethereum.request({ method: 'eth_requestAccounts' });
                    console.log('✅ Connected accounts:', connectedAccounts);
                }
                
                // Get current chain
                const chainId = await window.ethereum.request({ method: 'eth_chainId' });
                console.log('⛓️ Chain ID:', chainId);
                
                resultDiv.innerHTML = `
                    <div class="success">✅ Authentication Test Results:</div>
                    <div>
                        <strong>Provider:</strong> ${window.ethereum.isPurro ? 'Purro Wallet' : 'Other'}<br>
                        <strong>Accounts:</strong> ${JSON.stringify(accounts, null, 2)}<br>
                        <strong>Chain ID:</strong> ${chainId}<br>
                        <strong>Selected Address:</strong> ${window.ethereum.selectedAddress || 'None'}
                    </div>
                `;
                resultDiv.style.display = 'block';
                
            } catch (error) {
                console.error('❌ Auth test error:', error);
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                resultDiv.style.display = 'block';
            }
        }
        
        async function testSimpleSign() {
            const resultDiv = document.getElementById('authResult');
            
            try {
                console.log('🔍 Testing simple sign...');
                
                // Get accounts first
                const accounts = await window.ethereum.request({ method: 'eth_accounts' });
                
                if (accounts.length === 0) {
                    throw new Error('No accounts connected. Please connect first.');
                }
                
                console.log('📝 Attempting to sign message...');
                
                const message = 'Hello from Purro Wallet test!';
                const signature = await window.ethereum.request({
                    method: 'personal_sign',
                    params: [message, accounts[0]]
                });
                
                console.log('✅ Signature received:', signature);
                
                resultDiv.innerHTML = `
                    <div class="success">✅ Simple Sign Test Results:</div>
                    <div>
                        <strong>Message:</strong> ${message}<br>
                        <strong>Account:</strong> ${accounts[0]}<br>
                        <strong>Signature:</strong> ${signature}
                    </div>
                `;
                resultDiv.style.display = 'block';
                
            } catch (error) {
                console.error('❌ Simple sign error:', error);
                console.error('Error details:', {
                    name: error.name,
                    message: error.message,
                    code: error.code,
                    stack: error.stack
                });
                
                resultDiv.innerHTML = `
                    <div class="error">❌ Simple Sign Error:</div>
                    <div>
                        <strong>Message:</strong> ${error.message}<br>
                        <strong>Code:</strong> ${error.code || 'N/A'}<br>
                        <strong>Name:</strong> ${error.name || 'N/A'}
                    </div>
                `;
                resultDiv.style.display = 'block';
            }
        }
        
        // Auto-run auth test on load
        window.addEventListener('load', () => {
            console.log('🚀 Page loaded, running auth test...');
            setTimeout(testAuth, 1000);
        });
    </script>
</body>
</html>
