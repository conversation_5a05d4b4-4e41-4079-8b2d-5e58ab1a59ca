interface SignMessageProps {
  data: {
    message?: string;
    address?: string;
  };
  type: 'personal_sign' | 'eth_sign';
}

const SignMessage = ({ data, type }: SignMessageProps) => {
  const formatAddress = (address?: string) => {
    if (!address) return 'N/A';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const formatMessage = (message?: string) => {
    if (!message) return 'No message';
    
    // For eth_sign, message is in hex format
    if (type === 'eth_sign' && message.startsWith('0x')) {
      try {
        // Try to decode hex to string
        const hex = message.slice(2);
        const decoded = Buffer.from(hex, 'hex').toString('utf8');
        return decoded;
      } catch {
        // If decoding fails, show hex
        return message;
      }
    }
    
    return message;
  };

  const getMessageType = () => {
    switch (type) {
      case 'personal_sign':
        return 'Personal Message';
      case 'eth_sign':
        return 'Raw Message';
      default:
        return 'Message';
    }
  };

  const getDescription = () => {
    switch (type) {
      case 'personal_sign':
        return 'This is a human-readable message that will be signed with your private key.';
      case 'eth_sign':
        return 'This is a raw message that will be signed. Be careful as this could be used to sign transactions.';
      default:
        return 'This message will be signed with your private key.';
    }
  };

  const getWarningLevel = () => {
    return type === 'eth_sign' ? 'high' : 'medium';
  };

  return (
    <div className="space-y-4">
      <div className="bg-[var(--card-color)] rounded-lg p-4">
        <h3 className="text-base font-medium text-white mb-3">
          {getMessageType()}
        </h3>
        
        <div className="space-y-3">
          {data.address && (
            <div className="flex justify-between items-center py-2 border-b border-white/10">
              <span className="text-sm text-white/60">Signing Address</span>
              <span className="text-sm text-white font-mono" title={data.address}>
                {formatAddress(data.address)}
              </span>
            </div>
          )}
        </div>
      </div>

      <div className="bg-[var(--card-color)] rounded-lg p-4">
        <h3 className="text-base font-medium text-white mb-3">
          Message Content
        </h3>
        <div className="bg-[var(--background-color)] rounded-lg p-4 max-h-48 overflow-y-auto">
          <div className="text-sm text-white/90 whitespace-pre-wrap break-words font-mono">
            {formatMessage(data.message)}
          </div>
        </div>
        
        {type === 'eth_sign' && data.message?.startsWith('0x') && (
          <div className="mt-3 text-xs text-white/60">
            <p>Raw hex data: {data.message}</p>
          </div>
        )}
      </div>

      <div className={`rounded-lg p-3 border ${
        getWarningLevel() === 'high' 
          ? 'bg-red-500/10 border-red-500/20' 
          : 'bg-yellow-500/10 border-yellow-500/20'
      }`}>
        <div className="flex items-start gap-2">
          <div className={`size-4 rounded-full flex items-center justify-center mt-0.5 ${
            getWarningLevel() === 'high' 
              ? 'bg-red-500' 
              : 'bg-yellow-500'
          }`}>
            <span className="text-xs text-white">!</span>
          </div>
          <div className={`text-sm ${
            getWarningLevel() === 'high' 
              ? 'text-red-200' 
              : 'text-yellow-200'
          }`}>
            <p className="font-medium mb-1">
              {getWarningLevel() === 'high' ? 'High Risk Operation' : 'Sign Message'}
            </p>
            <p className={getWarningLevel() === 'high' ? 'text-red-200/80' : 'text-yellow-200/80'}>
              {getDescription()}
            </p>
            {getWarningLevel() === 'high' && (
              <p className="text-red-200/80 mt-2 font-medium">
                ⚠️ eth_sign can be used to sign transactions. Only approve if you trust this site completely.
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignMessage;
