<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Purro Wallet Signing Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background: #1a1a1a;
        color: #fff;
      }
      .container {
        background: #2a2a2a;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
      }
      button {
        background: #4caf50;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
      }
      button:hover {
        background: #45a049;
      }
      button:disabled {
        background: #666;
        cursor: not-allowed;
      }
      .result {
        background: #333;
        padding: 10px;
        border-radius: 5px;
        margin-top: 10px;
        word-break: break-all;
      }
      .error {
        background: #d32f2f;
        color: white;
        padding: 10px;
        border-radius: 5px;
        margin-top: 10px;
      }
      .success {
        background: #388e3c;
        color: white;
        padding: 10px;
        border-radius: 5px;
        margin-top: 10px;
      }
      input,
      textarea {
        width: 100%;
        padding: 8px;
        margin: 5px 0;
        background: #333;
        color: #fff;
        border: 1px solid #555;
        border-radius: 4px;
      }
      .status {
        padding: 10px;
        margin: 10px 0;
        border-radius: 5px;
        background: #333;
      }
    </style>
  </head>
  <body>
    <h1>🔐 Purro Wallet Signing Test</h1>

    <div class="container">
      <h2>Connection Status</h2>
      <div id="connectionStatus" class="status">Not connected</div>
      <button onclick="connectWallet()">Connect Wallet</button>
      <button onclick="checkConnection()">Check Connection</button>
      <button onclick="debugWalletState()">Debug Wallet State</button>
      <div id="debugInfo" class="result" style="display: none"></div>
    </div>

    <div class="container">
      <h2>Personal Sign Test</h2>
      <input
        type="text"
        id="personalMessage"
        placeholder="Message to sign"
        value="Hello, this is a test message!"
      />
      <button onclick="testPersonalSign()" id="personalSignBtn">
        Sign Personal Message
      </button>
      <div id="personalSignResult" class="result" style="display: none"></div>
    </div>

    <div class="container">
      <h2>Eth Sign Test</h2>
      <input
        type="text"
        id="ethMessage"
        placeholder="Hex message to sign"
        value="0x48656c6c6f20576f726c64"
      />
      <button onclick="testEthSign()" id="ethSignBtn">Sign Eth Message</button>
      <div id="ethSignResult" class="result" style="display: none"></div>
    </div>

    <div class="container">
      <h2>Typed Data Sign Test</h2>
      <textarea id="typedData" rows="10" placeholder="Typed data JSON">
{
  "types": {
    "EIP712Domain": [
      {"name": "name", "type": "string"},
      {"name": "version", "type": "string"},
      {"name": "chainId", "type": "uint256"},
      {"name": "verifyingContract", "type": "address"}
    ],
    "Person": [
      {"name": "name", "type": "string"},
      {"name": "wallet", "type": "address"}
    ],
    "Mail": [
      {"name": "from", "type": "Person"},
      {"name": "to", "type": "Person"},
      {"name": "contents", "type": "string"}
    ]
  },
  "primaryType": "Mail",
  "domain": {
    "name": "Ether Mail",
    "version": "1",
    "chainId": 1,
    "verifyingContract": "******************************************"
  },
  "message": {
    "from": {
      "name": "Cow",
      "wallet": "******************************************"
    },
    "to": {
      "name": "Bob",
      "wallet": "******************************************"
    },
    "contents": "Hello, Bob!"
  }
}
        </textarea
      >
      <button onclick="testTypedDataSign()" id="typedDataSignBtn">
        Sign Typed Data
      </button>
      <div id="typedDataSignResult" class="result" style="display: none"></div>
    </div>

    <div class="container">
      <h2>Transaction Sign Test</h2>
      <textarea
        id="transactionData"
        rows="6"
        placeholder="Transaction data JSON"
      >
{
  "to": "******************************************",
  "value": "0x0",
  "gasLimit": "0x5208",
  "gasPrice": "0x9184e72a000",
  "nonce": "0x0",
  "data": "0x"
}
        </textarea
      >
      <button onclick="testTransactionSign()" id="transactionSignBtn">
        Sign Transaction
      </button>
      <div
        id="transactionSignResult"
        class="result"
        style="display: none"
      ></div>
    </div>

    <script>
      let ethereum;
      let accounts = [];

      // Initialize
      window.addEventListener("load", async () => {
        if (typeof window.ethereum !== "undefined") {
          ethereum = window.ethereum;
          console.log("Ethereum provider detected");
          checkConnection();
        } else {
          document.getElementById("connectionStatus").innerHTML =
            '<span style="color: red;">No Ethereum provider detected. Please install Purro Wallet.</span>';
        }
      });

      async function connectWallet() {
        try {
          accounts = await ethereum.request({ method: "eth_requestAccounts" });
          document.getElementById(
            "connectionStatus"
          ).innerHTML = `<span style="color: green;">Connected: ${accounts[0]}</span>`;
          console.log("Connected accounts:", accounts);
        } catch (error) {
          console.error("Connection error:", error);
          document.getElementById(
            "connectionStatus"
          ).innerHTML = `<span style="color: red;">Connection failed: ${error.message}</span>`;
        }
      }

      async function checkConnection() {
        try {
          accounts = await ethereum.request({ method: "eth_accounts" });
          if (accounts.length > 0) {
            document.getElementById(
              "connectionStatus"
            ).innerHTML = `<span style="color: green;">Connected: ${accounts[0]}</span>`;
          } else {
            document.getElementById("connectionStatus").innerHTML =
              '<span style="color: orange;">Not connected</span>';
          }
        } catch (error) {
          console.error("Check connection error:", error);
          document.getElementById(
            "connectionStatus"
          ).innerHTML = `<span style="color: red;">Error: ${error.message}</span>`;
        }
      }

      async function debugWalletState() {
        const debugDiv = document.getElementById("debugInfo");

        try {
          console.log("🔍 Starting wallet debug...");

          // Check provider
          const providerInfo = {
            exists: !!window.ethereum,
            isPurro: window.ethereum?.isPurro,
            isMetaMask: window.ethereum?.isMetaMask,
            chainId: window.ethereum?.chainId,
            selectedAddress: window.ethereum?.selectedAddress,
          };

          // Check accounts
          let accountsInfo = "Not available";
          try {
            const accs = await ethereum.request({ method: "eth_accounts" });
            accountsInfo = accs.length > 0 ? accs : "No accounts connected";
          } catch (e) {
            accountsInfo = `Error: ${e.message}`;
          }

          // Check chain ID
          let chainInfo = "Not available";
          try {
            const chainId = await ethereum.request({ method: "eth_chainId" });
            chainInfo = chainId;
          } catch (e) {
            chainInfo = `Error: ${e.message}`;
          }

          debugDiv.innerHTML = `
                    <h4>🔍 Debug Information</h4>
                    <div style="text-align: left; font-family: monospace; font-size: 12px;">
                        <strong>Provider:</strong><br>
                        - Exists: ${providerInfo.exists}<br>
                        - Is Purro: ${providerInfo.isPurro}<br>
                        - Is MetaMask: ${providerInfo.isMetaMask}<br>
                        - Chain ID: ${providerInfo.chainId}<br>
                        - Selected Address: ${
                          providerInfo.selectedAddress
                        }<br><br>

                        <strong>Accounts:</strong><br>
                        ${JSON.stringify(accountsInfo, null, 2)}<br><br>

                        <strong>Current Chain:</strong><br>
                        ${chainInfo}<br><br>

                        <strong>Global accounts array:</strong><br>
                        ${JSON.stringify(accounts, null, 2)}
                    </div>
                `;
          debugDiv.style.display = "block";

          console.log("🔍 Debug info:", {
            providerInfo,
            accountsInfo,
            chainInfo,
            accounts,
          });
        } catch (error) {
          console.error("❌ Debug error:", error);
          debugDiv.innerHTML = `<div class="error">❌ Debug Error: ${error.message}</div>`;
          debugDiv.style.display = "block";
        }
      }

      async function testPersonalSign() {
        const message = document.getElementById("personalMessage").value;
        const resultDiv = document.getElementById("personalSignResult");
        const btn = document.getElementById("personalSignBtn");

        if (accounts.length === 0) {
          resultDiv.innerHTML = `<div class="error">❌ Please connect wallet first</div>`;
          resultDiv.style.display = "block";
          return;
        }

        btn.disabled = true;
        btn.textContent = "Signing...";

        try {
          console.log("🔄 Starting personal sign with:", {
            message,
            account: accounts[0],
          });

          const signature = await ethereum.request({
            method: "personal_sign",
            params: [message, accounts[0]],
          });

          resultDiv.innerHTML = `<div class="success">✅ Signature: ${signature}</div>`;
          resultDiv.style.display = "block";
          console.log("✅ Personal sign result:", signature);
        } catch (error) {
          console.error("❌ Personal sign error:", error);
          console.error("Error details:", {
            name: error.name,
            message: error.message,
            code: error.code,
            stack: error.stack,
          });
          resultDiv.innerHTML = `<div class="error">❌ Error: ${
            error.message
          }<br>Code: ${error.code || "N/A"}</div>`;
          resultDiv.style.display = "block";
        } finally {
          btn.disabled = false;
          btn.textContent = "Sign Personal Message";
        }
      }

      async function testEthSign() {
        const message = document.getElementById("ethMessage").value;
        const resultDiv = document.getElementById("ethSignResult");
        const btn = document.getElementById("ethSignBtn");

        if (accounts.length === 0) {
          resultDiv.innerHTML = `<div class="error">❌ Please connect wallet first</div>`;
          resultDiv.style.display = "block";
          return;
        }

        btn.disabled = true;
        btn.textContent = "Signing...";

        try {
          console.log("🔄 Starting eth sign with:", {
            message,
            account: accounts[0],
          });

          const signature = await ethereum.request({
            method: "eth_sign",
            params: [accounts[0], message],
          });

          resultDiv.innerHTML = `<div class="success">✅ Signature: ${signature}</div>`;
          resultDiv.style.display = "block";
          console.log("✅ Eth sign result:", signature);
        } catch (error) {
          console.error("❌ Eth sign error:", error);
          console.error("Error details:", {
            name: error.name,
            message: error.message,
            code: error.code,
            stack: error.stack,
          });
          resultDiv.innerHTML = `<div class="error">❌ Error: ${
            error.message
          }<br>Code: ${error.code || "N/A"}</div>`;
          resultDiv.style.display = "block";
        } finally {
          btn.disabled = false;
          btn.textContent = "Sign Eth Message";
        }
      }

      async function testTypedDataSign() {
        const typedDataStr = document.getElementById("typedData").value;
        const resultDiv = document.getElementById("typedDataSignResult");
        const btn = document.getElementById("typedDataSignBtn");

        btn.disabled = true;
        btn.textContent = "Signing...";

        try {
          const typedData = JSON.parse(typedDataStr);
          const signature = await ethereum.request({
            method: "eth_signTypedData_v4",
            params: [accounts[0], JSON.stringify(typedData)],
          });

          resultDiv.innerHTML = `<div class="success">✅ Signature: ${signature}</div>`;
          resultDiv.style.display = "block";
          console.log("Typed data sign result:", signature);
        } catch (error) {
          console.error("Typed data sign error:", error);
          resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
          resultDiv.style.display = "block";
        } finally {
          btn.disabled = false;
          btn.textContent = "Sign Typed Data";
        }
      }

      async function testTransactionSign() {
        const transactionStr = document.getElementById("transactionData").value;
        const resultDiv = document.getElementById("transactionSignResult");
        const btn = document.getElementById("transactionSignBtn");

        btn.disabled = true;
        btn.textContent = "Signing...";

        try {
          const transaction = JSON.parse(transactionStr);
          transaction.from = accounts[0];

          const signedTx = await ethereum.request({
            method: "eth_signTransaction",
            params: [transaction],
          });

          resultDiv.innerHTML = `<div class="success">✅ Signed Transaction: ${signedTx}</div>`;
          resultDiv.style.display = "block";
          console.log("Transaction sign result:", signedTx);
        } catch (error) {
          console.error("Transaction sign error:", error);
          resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
          resultDiv.style.display = "block";
        } finally {
          btn.disabled = false;
          btn.textContent = "Sign Transaction";
        }
      }
    </script>
  </body>
</html>
