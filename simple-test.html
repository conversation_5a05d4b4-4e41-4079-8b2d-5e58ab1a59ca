<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Signing Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        button:hover {
            background: #45a049;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .result {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
            word-break: break-all;
        }
        .error {
            background: #d32f2f;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        .success {
            background: #388e3c;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <h1>🔐 Simple Signing Test</h1>
    
    <div>
        <h2>Status: <span id="status">Checking...</span></h2>
        <button onclick="connect()" id="connectBtn">Connect Wallet</button>
        <button onclick="testSign()" id="signBtn" disabled>Test Sign</button>
    </div>
    
    <div id="result"></div>

    <script>
        let ethereum;
        let accounts = [];

        window.addEventListener('load', async () => {
            if (window.ethereum) {
                ethereum = window.ethereum;
                checkStatus();
            } else {
                document.getElementById('status').textContent = 'No wallet found';
            }
        });

        async function checkStatus() {
            try {
                accounts = await ethereum.request({ method: 'eth_accounts' });
                if (accounts.length > 0) {
                    document.getElementById('status').textContent = `Connected: ${accounts[0].slice(0, 8)}...`;
                    document.getElementById('signBtn').disabled = false;
                } else {
                    document.getElementById('status').textContent = 'Not connected';
                }
            } catch (error) {
                document.getElementById('status').textContent = 'Error checking status';
                console.error(error);
            }
        }

        async function connect() {
            const btn = document.getElementById('connectBtn');
            btn.disabled = true;
            btn.textContent = 'Connecting...';
            
            try {
                accounts = await ethereum.request({ method: 'eth_requestAccounts' });
                await checkStatus();
            } catch (error) {
                document.getElementById('result').innerHTML = `<div class="error">Connection failed: ${error.message}</div>`;
            } finally {
                btn.disabled = false;
                btn.textContent = 'Connect Wallet';
            }
        }

        async function testSign() {
            const btn = document.getElementById('signBtn');
            const resultDiv = document.getElementById('result');
            
            btn.disabled = true;
            btn.textContent = 'Signing...';
            
            try {
                console.log('🔄 Starting sign test...');
                
                const message = 'Hello from Purro Wallet!';
                console.log('📝 Message:', message);
                console.log('👤 Account:', accounts[0]);
                
                const signature = await ethereum.request({
                    method: 'personal_sign',
                    params: [message, accounts[0]]
                });
                
                console.log('✅ Signature received:', signature);
                
                resultDiv.innerHTML = `
                    <div class="success">
                        ✅ Signing successful!<br><br>
                        <strong>Message:</strong> ${message}<br>
                        <strong>Signature:</strong> ${signature}
                    </div>
                `;
                
            } catch (error) {
                console.error('❌ Signing failed:', error);
                
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ Signing failed!<br><br>
                        <strong>Error:</strong> ${error.message}<br>
                        <strong>Code:</strong> ${error.code || 'N/A'}
                    </div>
                `;
            } finally {
                btn.disabled = false;
                btn.textContent = 'Test Sign';
            }
        }
    </script>
</body>
</html>
