<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SIWE Test - Purro Wallet</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .message-box {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 SIWE (Sign-In with Ethereum) Test</h1>
        
        <div id="status" class="status disconnected">
            Status: Not connected
        </div>

        <div>
            <button id="connectBtn">Connect Wallet</button>
            <button id="siweBtn" disabled>Sign SIWE Message</button>
            <button id="clearLogBtn">Clear Log</button>
        </div>

        <div id="messagePreview" class="message-box" style="display: none;"></div>

        <div class="log" id="log"></div>
    </div>

    <script>
        let purroProvider = null;
        let isConnected = false;
        let currentAccount = null;

        // Logging function
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        // Update UI status
        function updateStatus(connected, account = null) {
            const statusElement = document.getElementById('status');
            const siweBtn = document.getElementById('siweBtn');
            const connectBtn = document.getElementById('connectBtn');

            isConnected = connected;
            currentAccount = account;

            if (connected && account) {
                statusElement.className = 'status connected';
                statusElement.textContent = `Status: Connected to ${account}`;
                siweBtn.disabled = false;
                connectBtn.disabled = true;
            } else {
                statusElement.className = 'status disconnected';
                statusElement.textContent = 'Status: Not connected';
                siweBtn.disabled = true;
                connectBtn.disabled = false;
            }
        }

        // Generate SIWE message
        function generateSIWEMessage(address) {
            const domain = window.location.host;
            const origin = window.location.origin;
            const statement = "Sign in with Ethereum to the app.";
            const nonce = Math.random().toString(36).substring(2, 15);
            const issuedAt = new Date().toISOString();
            
            return `${domain} wants you to sign in with your Ethereum account:
${address}

${statement}

URI: ${origin}
Version: 1
Chain ID: 1
Nonce: ${nonce}
Issued At: ${issuedAt}`;
        }

        // Initialize provider detection
        function initProvider() {
            log('🔍 Checking for Purro provider...');
            
            if (window.ethereum && window.ethereum.isPurro) {
                purroProvider = window.ethereum;
                log('✅ Purro provider found!', 'success');
                setupEventListeners();
                checkInitialConnection();
            } else {
                log('❌ Purro provider not found. Please install Purro wallet extension.', 'error');
                setTimeout(initProvider, 1000);
            }
        }

        // Setup event listeners
        function setupEventListeners() {
            if (!purroProvider) return;

            purroProvider.on('connect', (connectInfo) => {
                log(`🔗 Connect event: ${JSON.stringify(connectInfo)}`, 'success');
                if (connectInfo.accounts && connectInfo.accounts.length > 0) {
                    updateStatus(true, connectInfo.accounts[0]);
                }
            });

            purroProvider.on('disconnect', () => {
                log('🔌 Disconnect event', 'info');
                updateStatus(false);
            });

            purroProvider.on('accountsChanged', (accounts) => {
                log(`👤 Accounts changed: ${JSON.stringify(accounts)}`, 'info');
                if (accounts.length > 0) {
                    updateStatus(true, accounts[0]);
                } else {
                    updateStatus(false);
                }
            });

            log('📡 Event listeners set up');
        }

        // Check initial connection
        async function checkInitialConnection() {
            if (!purroProvider) return;

            try {
                log('🔍 Checking initial connection...');
                const accounts = await purroProvider.request({ method: 'eth_accounts' });
                log(`📋 Initial accounts: ${JSON.stringify(accounts)}`);
                
                if (accounts.length > 0) {
                    updateStatus(true, accounts[0]);
                    log('✅ Existing connection restored!', 'success');
                } else {
                    updateStatus(false);
                    log('ℹ️ No existing connection found');
                }
            } catch (error) {
                log(`❌ Error checking initial connection: ${error.message}`, 'error');
                updateStatus(false);
            }
        }

        // Connect wallet
        async function connectWallet() {
            if (!purroProvider) {
                log('❌ Provider not available', 'error');
                return;
            }

            try {
                log('🔄 Requesting wallet connection...');
                const accounts = await purroProvider.request({ method: 'eth_requestAccounts' });
                log(`✅ Connection successful! Accounts: ${JSON.stringify(accounts)}`, 'success');
                
                if (accounts.length > 0) {
                    updateStatus(true, accounts[0]);
                }
            } catch (error) {
                log(`❌ Connection failed: ${error.message}`, 'error');
                updateStatus(false);
            }
        }

        // Sign SIWE message
        async function signSIWE() {
            if (!purroProvider || !currentAccount) {
                log('❌ Provider or account not available', 'error');
                return;
            }

            try {
                // Generate SIWE message
                const siweMessage = generateSIWEMessage(currentAccount);
                
                // Show message preview
                const messagePreview = document.getElementById('messagePreview');
                messagePreview.textContent = siweMessage;
                messagePreview.style.display = 'block';
                
                log('📝 Generated SIWE message:', 'info');
                log(siweMessage, 'info');
                
                log('🔄 Requesting signature...');
                
                // Sign the message using personal_sign
                const signature = await purroProvider.request({
                    method: 'personal_sign',
                    params: [siweMessage, currentAccount]
                });
                
                log(`✅ SIWE signature successful!`, 'success');
                log(`Signature: ${signature}`, 'success');
                
                // Verify signature (optional)
                log('🔍 Signature details:', 'info');
                log(`Message length: ${siweMessage.length} characters`, 'info');
                log(`Signature length: ${signature.length} characters`, 'info');
                log(`Signature format: ${signature.startsWith('0x') ? 'Valid hex' : 'Invalid format'}`, 'info');
                
            } catch (error) {
                log(`❌ SIWE signing failed: ${error.message}`, 'error');
                console.error('SIWE Error:', error);
            }
        }

        // Clear log
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('messagePreview').style.display = 'none';
        }

        // Event listeners for buttons
        document.getElementById('connectBtn').addEventListener('click', connectWallet);
        document.getElementById('siweBtn').addEventListener('click', signSIWE);
        document.getElementById('clearLogBtn').addEventListener('click', clearLog);

        // Initialize when page loads
        window.addEventListener('load', () => {
            log('🚀 SIWE test page loaded, initializing...');
            initProvider();
        });

        // Also try to initialize immediately
        initProvider();
    </script>
</body>
</html>
