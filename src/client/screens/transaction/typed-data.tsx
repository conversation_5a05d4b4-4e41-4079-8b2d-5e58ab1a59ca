interface TypedDataSignProps {
  data: {
    address?: string;
    typedData?: {
      types: any;
      domain: any;
      primaryType: string;
      message: any;
    };
  };
}

const TypedDataSign = ({ data }: TypedDataSignProps) => {
  const formatAddress = (address?: string) => {
    if (!address) return 'N/A';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const renderTypedDataField = (key: string, value: any, level = 0) => {
    const indent = level * 16;
    
    if (typeof value === 'object' && value !== null) {
      return (
        <div key={key} style={{ marginLeft: indent }}>
          <div className="text-white/80 font-medium">{key}:</div>
          <div className="ml-4">
            {Object.entries(value).map(([subKey, subValue]) =>
              renderTypedDataField(subKey, subValue, level + 1)
            )}
          </div>
        </div>
      );
    }
    
    return (
      <div key={key} className="flex justify-between items-start py-1" style={{ marginLeft: indent }}>
        <span className="text-white/60 text-sm">{key}:</span>
        <span className="text-white/90 text-sm text-right max-w-[200px] break-words">
          {String(value)}
        </span>
      </div>
    );
  };

  const renderDomain = () => {
    if (!data.typedData?.domain) return null;
    
    return (
      <div className="bg-[var(--card-color)] rounded-lg p-4">
        <h3 className="text-base font-medium text-white mb-3">Domain</h3>
        <div className="space-y-2">
          {Object.entries(data.typedData.domain).map(([key, value]) =>
            renderTypedDataField(key, value)
          )}
        </div>
      </div>
    );
  };

  const renderMessage = () => {
    if (!data.typedData?.message) return null;
    
    return (
      <div className="bg-[var(--card-color)] rounded-lg p-4">
        <h3 className="text-base font-medium text-white mb-3">Message</h3>
        <div className="space-y-2">
          {Object.entries(data.typedData.message).map(([key, value]) =>
            renderTypedDataField(key, value)
          )}
        </div>
      </div>
    );
  };

  const renderTypes = () => {
    if (!data.typedData?.types) return null;
    
    return (
      <div className="bg-[var(--card-color)] rounded-lg p-4">
        <h3 className="text-base font-medium text-white mb-3">Types Definition</h3>
        <div className="bg-[var(--background-color)] rounded-lg p-3 max-h-48 overflow-y-auto">
          <pre className="text-xs text-white/80 font-mono">
            {JSON.stringify(data.typedData.types, null, 2)}
          </pre>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      <div className="bg-[var(--card-color)] rounded-lg p-4">
        <h3 className="text-base font-medium text-white mb-3">
          Typed Data Signature
        </h3>
        
        <div className="space-y-3">
          {data.address && (
            <div className="flex justify-between items-center py-2 border-b border-white/10">
              <span className="text-sm text-white/60">Signing Address</span>
              <span className="text-sm text-white font-mono" title={data.address}>
                {formatAddress(data.address)}
              </span>
            </div>
          )}
          
          {data.typedData?.primaryType && (
            <div className="flex justify-between items-center py-2">
              <span className="text-sm text-white/60">Primary Type</span>
              <span className="text-sm text-white font-medium">
                {data.typedData.primaryType}
              </span>
            </div>
          )}
        </div>
      </div>

      {renderDomain()}
      {renderMessage()}
      {renderTypes()}

      <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
        <div className="flex items-start gap-2">
          <div className="size-4 rounded-full bg-blue-500 flex items-center justify-center mt-0.5">
            <span className="text-xs text-white">i</span>
          </div>
          <div className="text-sm text-blue-200">
            <p className="font-medium mb-1">EIP-712 Typed Data</p>
            <p className="text-blue-200/80">
              This is a structured data signature that follows the EIP-712 standard. 
              It's safer than raw message signing as it provides clear context about what you're signing.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TypedDataSign;
