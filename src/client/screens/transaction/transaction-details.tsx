import { formatCurrency } from "@/client/utils/formatters";

interface TransactionDetailsProps {
  data: {
    to?: string;
    value?: string;
    gas?: string;
    gasPrice?: string;
    data?: string;
    from?: string;
  };
  type: 'send' | 'sign';
}

const TransactionDetails = ({ data, type }: TransactionDetailsProps) => {
  const formatEthValue = (value?: string) => {
    if (!value || value === '0x0') return '0 ETH';
    const wei = parseInt(value, 16);
    const eth = wei / 1e18;
    return `${formatCurrency(eth, 6)} ETH`;
  };

  const formatGasPrice = (gasPrice?: string) => {
    if (!gasPrice) return 'N/A';
    const wei = parseInt(gasPrice, 16);
    const gwei = wei / 1e9;
    return `${formatCurrency(gwei, 2)} Gwei`;
  };

  const calculateMaxFee = (gas?: string, gasPrice?: string) => {
    if (!gas || !gasPrice) return 'N/A';
    const gasLimit = parseInt(gas, 16);
    const gasPriceWei = parseInt(gasPrice, 16);
    const maxFeeWei = gasLimit * gasPriceWei;
    const maxFeeEth = maxFeeWei / 1e18;
    return `${formatCurrency(maxFeeEth, 6)} ETH`;
  };

  const formatAddress = (address?: string) => {
    if (!address) return 'N/A';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  return (
    <div className="space-y-4">
      <div className="bg-[var(--card-color)] rounded-lg p-4">
        <h3 className="text-base font-medium text-white mb-3">
          Transaction Details
        </h3>
        
        <div className="space-y-3">
          {data.from && (
            <div className="flex justify-between items-center py-2 border-b border-white/10">
              <span className="text-sm text-white/60">From</span>
              <span className="text-sm text-white font-mono" title={data.from}>
                {formatAddress(data.from)}
              </span>
            </div>
          )}
          
          <div className="flex justify-between items-center py-2 border-b border-white/10">
            <span className="text-sm text-white/60">To</span>
            <span className="text-sm text-white font-mono" title={data.to}>
              {formatAddress(data.to)}
            </span>
          </div>
          
          <div className="flex justify-between items-center py-2 border-b border-white/10">
            <span className="text-sm text-white/60">Value</span>
            <span className="text-sm text-white font-medium">
              {formatEthValue(data.value)}
            </span>
          </div>
          
          <div className="flex justify-between items-center py-2 border-b border-white/10">
            <span className="text-sm text-white/60">Gas Limit</span>
            <span className="text-sm text-white">
              {data.gas ? parseInt(data.gas, 16).toLocaleString() : 'N/A'}
            </span>
          </div>
          
          <div className="flex justify-between items-center py-2 border-b border-white/10">
            <span className="text-sm text-white/60">Gas Price</span>
            <span className="text-sm text-white">
              {formatGasPrice(data.gasPrice)}
            </span>
          </div>
          
          <div className="flex justify-between items-center py-2">
            <span className="text-sm text-white/60">Max Fee</span>
            <span className="text-sm text-white font-medium">
              {calculateMaxFee(data.gas, data.gasPrice)}
            </span>
          </div>
        </div>
      </div>

      {data.data && data.data !== '0x' && (
        <div className="bg-[var(--card-color)] rounded-lg p-4">
          <h3 className="text-base font-medium text-white mb-3">
            Transaction Data
          </h3>
          <div className="bg-[var(--background-color)] rounded-lg p-3">
            <code className="text-xs text-white/80 font-mono break-all">
              {data.data}
            </code>
          </div>
        </div>
      )}

      <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
        <div className="flex items-start gap-2">
          <div className="size-4 rounded-full bg-blue-500 flex items-center justify-center mt-0.5">
            <span className="text-xs text-white">i</span>
          </div>
          <div className="text-sm text-blue-200">
            <p className="font-medium mb-1">
              {type === 'send' ? 'Send Transaction' : 'Sign Transaction'}
            </p>
            <p className="text-blue-200/80">
              {type === 'send' 
                ? 'This transaction will be broadcast to the network and cannot be reversed.'
                : 'This will create a signed transaction that can be broadcast later.'
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransactionDetails;
