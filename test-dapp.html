<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Test dApp - Purro Extension</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      button {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 10px 0;
      }
      button:hover {
        background: #0056b3;
      }
      .result {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 15px;
        margin: 10px 0;
        white-space: pre-wrap;
        font-family: monospace;
      }
      .error {
        background: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
      }
      .success {
        background: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
      }
    </style>
  </head>
  <body>
    <h1>Test dApp - Purro Extension</h1>
    <p>This page tests the Purro wallet extension integration.</p>

    <div>
      <h2>Connection Tests</h2>
      <button onclick="testRequestAccounts()">Test eth_requestAccounts</button>
      <button onclick="testGetAccounts()">Test eth_accounts</button>
      <button onclick="testChainId()">Test eth_chainId</button>
      <button onclick="testSwitchChain()">
        Test wallet_switchEthereumChain
      </button>
      <button onclick="checkProvider()">Check Provider</button>
    </div>

    <div>
      <h2>🔐 Transaction Tests</h2>
      <button onclick="testSendTransaction()">Test eth_sendTransaction</button>
      <button onclick="testSignTransaction()">Test eth_signTransaction</button>
      <button onclick="testPersonalSign()">Test personal_sign</button>
      <button onclick="testEthSign()">Test eth_sign</button>
      <button onclick="testSignTypedData()">Test eth_signTypedData_v4</button>
    </div>

    <div id="results"></div>

    <script>
      function addResult(title, content, isError = false) {
        const resultsDiv = document.getElementById("results");
        const resultDiv = document.createElement("div");
        resultDiv.className = `result ${isError ? "error" : "success"}`;
        resultDiv.innerHTML = `<strong>${title}:</strong>\n${JSON.stringify(
          content,
          null,
          2
        )}`;
        resultsDiv.appendChild(resultDiv);
      }

      function checkProvider() {
        if (typeof window.ethereum !== "undefined") {
          addResult("Provider Check", {
            exists: true,
            isMetaMask: window.ethereum.isMetaMask,
            isPurro: window.ethereum.isPurro,
            chainId: window.ethereum.chainId,
            selectedAddress: window.ethereum.selectedAddress,
          });
        } else {
          addResult("Provider Check", { exists: false }, true);
        }
      }

      async function testRequestAccounts() {
        try {
          console.log("🔍 Testing eth_requestAccounts...");
          const accounts = await window.ethereum.request({
            method: "eth_requestAccounts",
          });
          console.log("✅ eth_requestAccounts result:", accounts);

          addResult("eth_requestAccounts", {
            success: true,
            accounts: accounts,
            type: Array.isArray(accounts) ? "array" : typeof accounts,
            length: Array.isArray(accounts) ? accounts.length : "N/A",
          });
        } catch (error) {
          console.error("❌ eth_requestAccounts error:", error);
          addResult(
            "eth_requestAccounts",
            {
              success: false,
              error: error.message,
            },
            true
          );
        }
      }

      async function testGetAccounts() {
        try {
          console.log("🔍 Testing eth_accounts...");
          const accounts = await window.ethereum.request({
            method: "eth_accounts",
          });
          console.log("✅ eth_accounts result:", accounts);

          addResult("eth_accounts", {
            success: true,
            accounts: accounts,
            type: Array.isArray(accounts) ? "array" : typeof accounts,
            length: Array.isArray(accounts) ? accounts.length : "N/A",
          });
        } catch (error) {
          console.error("❌ eth_accounts error:", error);
          addResult(
            "eth_accounts",
            {
              success: false,
              error: error.message,
            },
            true
          );
        }
      }

      async function testChainId() {
        try {
          console.log("🔍 Testing eth_chainId...");
          const chainId = await window.ethereum.request({
            method: "eth_chainId",
          });
          console.log("✅ eth_chainId result:", chainId);

          addResult("eth_chainId", {
            success: true,
            chainId: chainId,
            type: typeof chainId,
          });
        } catch (error) {
          console.error("❌ eth_chainId error:", error);
          addResult(
            "eth_chainId",
            {
              success: false,
              error: error.message,
            },
            true
          );
        }
      }

      async function testSwitchChain() {
        try {
          console.log("🔍 Testing wallet_switchEthereumChain...");

          // Test switching to different chains
          const chains = [
            { name: "Ethereum", chainId: "0x1" },
            { name: "Arbitrum", chainId: "0xa4b1" },
            { name: "Base", chainId: "0x2105" },
            { name: "HyperEVM", chainId: "0x3e7" },
          ];

          const selectedChain =
            chains[Math.floor(Math.random() * chains.length)];

          const result = await window.ethereum.request({
            method: "wallet_switchEthereumChain",
            params: [{ chainId: selectedChain.chainId }],
          });

          console.log("✅ wallet_switchEthereumChain result:", result);

          // Get current chain ID after switch
          const newChainId = await window.ethereum.request({
            method: "eth_chainId",
          });

          addResult("wallet_switchEthereumChain", {
            success: true,
            requestedChain: selectedChain,
            result: result,
            newChainId: newChainId,
            switchSuccessful: newChainId === selectedChain.chainId,
          });
        } catch (error) {
          console.error("❌ wallet_switchEthereumChain error:", error);
          addResult(
            "wallet_switchEthereumChain",
            {
              success: false,
              error: error.message,
              code: error.code,
            },
            true
          );
        }
      }

      // Auto-check provider on load
      window.addEventListener("load", () => {
        setTimeout(checkProvider, 1000);
      });

      // Listen for account changes
      if (typeof window.ethereum !== "undefined") {
        window.ethereum.on("accountsChanged", (accounts) => {
          console.log("📢 accountsChanged event:", accounts);
          addResult("accountsChanged Event", {
            accounts: accounts,
            type: Array.isArray(accounts) ? "array" : typeof accounts,
            length: Array.isArray(accounts) ? accounts.length : "N/A",
          });
        });

        window.ethereum.on("chainChanged", (chainId) => {
          console.log("📢 chainChanged event:", chainId);
          addResult("chainChanged Event", {
            chainId: chainId,
            type: typeof chainId,
          });
        });
      }

      // Transaction test functions
      async function testSendTransaction() {
        try {
          console.log("🔍 Testing eth_sendTransaction...");

          const accounts = await window.ethereum.request({
            method: "eth_accounts",
          });
          if (accounts.length === 0) {
            throw new Error("No accounts connected");
          }

          const txHash = await window.ethereum.request({
            method: "eth_sendTransaction",
            params: [
              {
                from: accounts[0],
                to: "******************************************",
                value: "0x16345785d8a0000", // 0.1 ETH
                gas: "0x5208", // 21000
                gasPrice: "0x4a817c800", // 20 gwei
              },
            ],
          });

          console.log("✅ eth_sendTransaction result:", txHash);
          addResult("eth_sendTransaction", {
            success: true,
            transactionHash: txHash,
          });
        } catch (error) {
          console.error("❌ eth_sendTransaction error:", error);
          addResult(
            "eth_sendTransaction",
            {
              success: false,
              error: error.message,
            },
            true
          );
        }
      }

      async function testSignTransaction() {
        try {
          console.log("🔍 Testing eth_signTransaction...");

          const accounts = await window.ethereum.request({
            method: "eth_accounts",
          });
          if (accounts.length === 0) {
            throw new Error("No accounts connected");
          }

          const signature = await window.ethereum.request({
            method: "eth_signTransaction",
            params: [
              {
                from: accounts[0],
                to: "******************************************",
                value: "0x16345785d8a0000",
                gas: "0x5208",
                gasPrice: "0x4a817c800",
              },
            ],
          });

          console.log("✅ eth_signTransaction result:", signature);
          addResult("eth_signTransaction", {
            success: true,
            signature: signature,
          });
        } catch (error) {
          console.error("❌ eth_signTransaction error:", error);
          addResult(
            "eth_signTransaction",
            {
              success: false,
              error: error.message,
            },
            true
          );
        }
      }

      async function testPersonalSign() {
        try {
          console.log("🔍 Testing personal_sign...");

          const accounts = await window.ethereum.request({
            method: "eth_accounts",
          });
          if (accounts.length === 0) {
            throw new Error("No accounts connected");
          }

          const message = "Hello, this is a test message for signing!";
          const signature = await window.ethereum.request({
            method: "personal_sign",
            params: [message, accounts[0]],
          });

          console.log("✅ personal_sign result:", signature);
          addResult("personal_sign", {
            success: true,
            message: message,
            signature: signature,
          });
        } catch (error) {
          console.error("❌ personal_sign error:", error);
          addResult(
            "personal_sign",
            {
              success: false,
              error: error.message,
            },
            true
          );
        }
      }

      async function testEthSign() {
        try {
          console.log("🔍 Testing eth_sign...");

          const accounts = await window.ethereum.request({
            method: "eth_accounts",
          });
          if (accounts.length === 0) {
            throw new Error("No accounts connected");
          }

          const message = "0x48656c6c6f20576f726c64"; // "Hello World" in hex
          const signature = await window.ethereum.request({
            method: "eth_sign",
            params: [accounts[0], message],
          });

          console.log("✅ eth_sign result:", signature);
          addResult("eth_sign", {
            success: true,
            message: message,
            signature: signature,
          });
        } catch (error) {
          console.error("❌ eth_sign error:", error);
          addResult(
            "eth_sign",
            {
              success: false,
              error: error.message,
            },
            true
          );
        }
      }

      async function testSignTypedData() {
        try {
          console.log("🔍 Testing eth_signTypedData_v4...");

          const accounts = await window.ethereum.request({
            method: "eth_accounts",
          });
          if (accounts.length === 0) {
            throw new Error("No accounts connected");
          }

          const typedData = {
            types: {
              EIP712Domain: [
                { name: "name", type: "string" },
                { name: "version", type: "string" },
                { name: "chainId", type: "uint256" },
                { name: "verifyingContract", type: "address" },
              ],
              Message: [
                { name: "content", type: "string" },
                { name: "timestamp", type: "uint256" },
              ],
            },
            domain: {
              name: "Test dApp",
              version: "1",
              chainId: 999,
              verifyingContract: "******************************************",
            },
            primaryType: "Message",
            message: {
              content: "Hello, World! This is a typed data signature test.",
              timestamp: Math.floor(Date.now() / 1000),
            },
          };

          const signature = await window.ethereum.request({
            method: "eth_signTypedData_v4",
            params: [accounts[0], JSON.stringify(typedData)],
          });

          console.log("✅ eth_signTypedData_v4 result:", signature);
          addResult("eth_signTypedData_v4", {
            success: true,
            typedData: typedData,
            signature: signature,
          });
        } catch (error) {
          console.error("❌ eth_signTypedData_v4 error:", error);
          addResult(
            "eth_signTypedData_v4",
            {
              success: false,
              error: error.message,
            },
            true
          );
        }
      }
    </script>
  </body>
</html>
