import { ethers } from 'ethers';
import { accountHandler } from '../handlers/account-handler';
import { authHandler } from '../handlers/auth-handler';

export interface SigningResult {
    signature: string;
    messageHash?: string;
}

export interface TransactionSigningResult {
    signedTransaction: string;
    transactionHash?: string;
}

/**
 * EVM Signing Utilities
 * Provides real cryptographic signing for EVM chains using ethers.js
 */
export const evmSigningUtils = {
    /**
     * Sign a personal message (personal_sign)
     * @param message - The message to sign (string or hex)
     * @param accountId - The account ID to sign with
     * @returns Promise<SigningResult>
     */
    async signPersonalMessage(message: string, accountId: string): Promise<SigningResult> {
        try {
            console.log('🔐 Signing personal message for account:', accountId);
            console.log('📝 Message to sign:', message);

            // Validate authentication first
            console.log('🔒 Checking authentication...');
            await signingAuthUtils.requireAuthentication();
            console.log('✅ Authentication validated');

            // Get the private key for the account
            console.log('🔑 Getting private key for account...');
            const privateKey = await accountHandler.getPrivateKeyByAccountId(accountId);
            console.log('✅ Private key retrieved (length:', privateKey.length, ')');

            // Create wallet instance
            console.log('👛 Creating wallet instance...');
            const wallet = new ethers.Wallet(privateKey);
            console.log('✅ Wallet created, address:', wallet.address);

            // Sign the message
            console.log('✍️ Signing message...');
            const signature = await wallet.signMessage(message);
            console.log('✅ Message signed, signature:', signature);

            // Calculate message hash for verification
            const messageHash = ethers.hashMessage(message);
            console.log('🔍 Message hash:', messageHash);

            console.log('✅ Personal message signed successfully');

            return {
                signature,
                messageHash
            };
        } catch (error) {
            console.error('❌ Error signing personal message:', error);
            console.error('❌ Error details:', {
                name: error instanceof Error ? error.name : 'Unknown',
                message: error instanceof Error ? error.message : String(error),
                stack: error instanceof Error ? error.stack : 'No stack'
            });
            throw new Error(`Failed to sign personal message: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },

    /**
     * Sign a raw message (eth_sign)
     * @param message - The message to sign (hex string)
     * @param accountId - The account ID to sign with
     * @returns Promise<SigningResult>
     */
    async signRawMessage(message: string, accountId: string): Promise<SigningResult> {
        try {
            console.log('🔐 Signing raw message for account:', accountId);

            // Get the private key for the account
            const privateKey = await accountHandler.getPrivateKeyByAccountId(accountId);

            // Create wallet instance
            const wallet = new ethers.Wallet(privateKey);

            // Ensure message is in bytes format
            let messageBytes: Uint8Array;
            if (message.startsWith('0x')) {
                messageBytes = ethers.getBytes(message);
            } else {
                messageBytes = ethers.toUtf8Bytes(message);
            }

            // Sign the raw message hash
            const messageHash = ethers.keccak256(messageBytes);
            const signature = await wallet.signMessage(messageBytes);

            console.log('✅ Raw message signed successfully');

            return {
                signature,
                messageHash
            };
        } catch (error) {
            console.error('❌ Error signing raw message:', error);
            throw new Error(`Failed to sign raw message: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },

    /**
     * Sign typed data (EIP-712)
     * @param typedData - The typed data object
     * @param accountId - The account ID to sign with
     * @returns Promise<SigningResult>
     */
    async signTypedData(typedData: any, accountId: string): Promise<SigningResult> {
        try {
            console.log('🔐 Signing typed data for account:', accountId);

            // Get the private key for the account
            const privateKey = await accountHandler.getPrivateKeyByAccountId(accountId);

            // Create wallet instance
            const wallet = new ethers.Wallet(privateKey);

            // Extract domain, types, and message from typed data
            const { domain, types, message } = typedData;

            // Remove EIP712Domain from types if it exists (ethers handles this automatically)
            const cleanTypes = { ...types };
            delete cleanTypes.EIP712Domain;

            // Sign the typed data
            const signature = await wallet.signTypedData(domain, cleanTypes, message);

            // Calculate typed data hash for verification
            const messageHash = ethers.TypedDataEncoder.hash(domain, cleanTypes, message);

            console.log('✅ Typed data signed successfully');

            return {
                signature,
                messageHash
            };
        } catch (error) {
            console.error('❌ Error signing typed data:', error);
            throw new Error(`Failed to sign typed data: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },

    /**
     * Sign a transaction
     * @param transactionData - The transaction object
     * @param accountId - The account ID to sign with
     * @returns Promise<TransactionSigningResult>
     */
    async signTransaction(transactionData: any, accountId: string): Promise<TransactionSigningResult> {
        try {
            console.log('🔐 Signing transaction for account:', accountId);

            // Get the private key for the account
            const privateKey = await accountHandler.getPrivateKeyByAccountId(accountId);

            // Create wallet instance
            const wallet = new ethers.Wallet(privateKey);

            // Prepare transaction object
            const transaction = {
                to: transactionData.to,
                value: transactionData.value || '0x0',
                gasLimit: transactionData.gasLimit || transactionData.gas || '0x5208', // 21000 in hex
                gasPrice: transactionData.gasPrice,
                maxFeePerGas: transactionData.maxFeePerGas,
                maxPriorityFeePerGas: transactionData.maxPriorityFeePerGas,
                nonce: transactionData.nonce,
                data: transactionData.data || '0x',
                chainId: transactionData.chainId,
                type: transactionData.type
            };

            // Remove undefined values
            Object.keys(transaction).forEach(key => {
                if (transaction[key as keyof typeof transaction] === undefined) {
                    delete transaction[key as keyof typeof transaction];
                }
            });

            // Sign the transaction
            const signedTransaction = await wallet.signTransaction(transaction);

            // Parse the signed transaction to get hash
            const parsedTx = ethers.Transaction.from(signedTransaction);
            const transactionHash = parsedTx.hash;

            console.log('✅ Transaction signed successfully');

            return {
                signedTransaction,
                transactionHash
            };
        } catch (error) {
            console.error('❌ Error signing transaction:', error);
            throw new Error(`Failed to sign transaction: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },

    /**
     * Verify a signature
     * @param message - The original message
     * @param signature - The signature to verify
     * @param expectedAddress - The expected signer address
     * @returns Promise<boolean>
     */
    async verifySignature(message: string, signature: string, expectedAddress: string): Promise<boolean> {
        try {
            const recoveredAddress = ethers.verifyMessage(message, signature);
            return recoveredAddress.toLowerCase() === expectedAddress.toLowerCase();
        } catch (error) {
            console.error('❌ Error verifying signature:', error);
            return false;
        }
    },

    /**
     * Get account address by account ID
     * @param accountId - The account ID
     * @returns Promise<string>
     */
    async getAccountAddress(accountId: string): Promise<string> {
        try {
            const privateKey = await accountHandler.getPrivateKeyByAccountId(accountId);
            const wallet = new ethers.Wallet(privateKey);
            return wallet.address;
        } catch (error) {
            console.error('❌ Error getting account address:', error);
            throw new Error(`Failed to get account address: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
};

/**
 * Development mode flag - set to true to bypass authentication for testing
 * WARNING: Only use in development, never in production!
 */
const DEV_BYPASS_AUTH = true; // TODO: Remove this in production

/**
 * Utility to validate authentication before signing
 */
export const signingAuthUtils = {
    /**
     * Validate that user is authenticated before signing
     * @returns Promise<boolean>
     */
    async validateAuthentication(): Promise<boolean> {
        try {
            console.log('🔒 Validating authentication...');
            const session = await authHandler.getSession();
            console.log('📋 Session data:', session ? {
                timestamp: session.timestamp,
                expiresAt: session.expiresAt,
                currentTime: Date.now(),
                isExpired: session.expiresAt <= Date.now()
            } : 'No session');

            const isValid = session !== null && session.expiresAt > Date.now();
            console.log('✅ Authentication valid:', isValid);
            return isValid;
        } catch (error) {
            console.error('❌ Error validating authentication:', error);
            return false;
        }
    },

    /**
     * Ensure user is authenticated, throw error if not
     * @throws Error if user is not authenticated
     */
    async requireAuthentication(): Promise<void> {
        console.log('🔒 Requiring authentication...');
        const isAuthenticated = await this.validateAuthentication();
        if (!isAuthenticated) {
            console.error('❌ Authentication required but not valid');
            throw new Error('User must be authenticated to sign transactions. Please unlock your wallet.');
        }
        console.log('✅ Authentication requirement satisfied');
    }
};
